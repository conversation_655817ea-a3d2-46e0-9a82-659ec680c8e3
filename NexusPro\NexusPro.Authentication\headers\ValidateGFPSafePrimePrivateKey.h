/*
 * ValidateGFPSafePrimePrivateKey.h
 * Original Function: CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>::Validate
 * Original Address: 0x140637C30
 * Original File: ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.h
 *
 * Header for GFP safe prime private key validation function.
 */

#pragma once
#ifndef VALIDATEGFPSAFEPRIMEPRIVATEKEY_H
#define VALIDATEGFPSAFEPRIMEPRIVATEKEY_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * Validate GFP safe prime private key implementation
 * @param this_ptr Pointer to DL_PrivateKeyImpl object
 * @param rng Random number generator for validation
 * @param level Validation level (0=basic, 1=thorough, 2=complete)
 * @return true if validation passes, false otherwise
 */
bool ValidateGFPSafePrimePrivateKey(void* this_ptr, void* rng, unsigned int level);

#endif // VALIDATEGFPSAFEPRIMEPRIVATEKEY_H
