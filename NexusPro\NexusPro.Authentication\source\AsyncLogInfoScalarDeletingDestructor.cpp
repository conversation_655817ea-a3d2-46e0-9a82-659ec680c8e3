/*
 * AsyncLogInfoScalarDeletingDestructor.cpp
 * Original Function: CAsyncLogInfo::scalar deleting destructor
 * Address: 0x1403C14F0
 *
 * <PERSON>alar deleting destructor for CAsyncLogInfo class.
 * Calls destructor and optionally deletes the object memory.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Scalar deleting destructor for CAsyncLogInfo
 * Calls destructor and optionally deletes the object memory
 * @param this Pointer to the CAsyncLogInfo object
 * @param a2 Deletion flags (bit 0 = delete memory)
 * @return Pointer to the object (for chaining)
 */
CAsyncLogInfo* AsyncLogInfoScalarDeletingDestructor(CAsyncLogInfo* this_ptr, int a2)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    CAsyncLogInfo* v6; // [sp+30h] [bp+8h]@1
    int v7; // [sp+38h] [bp+10h]@1

    v7 = a2;
    v6 = this_ptr;
    v2 = &v5;
    
    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Call the destructor
    AsyncLogInfoDestructor(v6);
    
    // Delete memory if requested
    if (v7 & 1) {
        operator delete(v6);
    }
    
    return v6;
}
