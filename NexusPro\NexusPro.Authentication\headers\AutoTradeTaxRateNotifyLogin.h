/*
 * AutoTradeTaxRateNotifyLogin.h
 * Original Function: TRC_AutoTrade::SendMsg_UserLogInNotifyTaxRate
 * Original Address: 0x1402D8540
 * Original File: SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.h
 *
 * Header for auto trade tax rate notification on login.
 */

#pragma once
#ifndef AUTOTRADETAXRATENOTIFYLOGIN_H
#define AUTOTRADETAXRATENOTIFYLOGIN_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * Send tax rate notification on user login to auto trade system
 * @param this_ptr Pointer to TRC_AutoTrade object
 * @param n Tax rate parameter
 */
void AutoTradeTaxRateNotifyLogin(void* this_ptr, int n);

#endif // AUTOTRADETAXRATENOTIFYLOGIN_H
