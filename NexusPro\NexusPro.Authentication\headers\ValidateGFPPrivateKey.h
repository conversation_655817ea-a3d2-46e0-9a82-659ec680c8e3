/*
 * ValidateGFPPrivateKey.h
 * Original Function: CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_GFP>::Validate
 * Original Address: 0x140635EE0
 * Original File: ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.h
 *
 * Header for GFP private key validation function.
 */

#pragma once
#ifndef VALIDATEGFPPRIVATEKEY_H
#define VALIDATEGFPPRIVATEKEY_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * Validate GFP private key implementation
 * @param this_ptr Pointer to DL_PrivateKeyImpl object
 * @param rng Random number generator for validation
 * @param level Validation level (0=basic, 1=thorough, 2=complete)
 * @return true if validation passes, false otherwise
 */
bool ValidateGFPPrivateKey(void* this_ptr, void* rng, unsigned int level);

#endif // VALIDATEGFPPRIVATEKEY_H
