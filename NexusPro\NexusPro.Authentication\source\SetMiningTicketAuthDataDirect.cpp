/*
 * SetMiningTicketAuthDataDirect.cpp
 * Original Function: MiningTicket::_AuthKeyTicket::Set (overload)
 * Original Address: 0x140078ED0
 * Original File: Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.cpp
 *
 * Sets the authentication key ticket data directly from a source value.
 * This is a simple overload that copies the entire 32-bit data field at once.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Set mining ticket authentication key data directly
 * @param this_ptr Pointer to MiningTicket::_AuthKeyTicket object
 * @param uiSrc Source data value to copy
 */
void SetMiningTicketAuthDataDirect(void* this_ptr, unsigned int uiSrc) 
{
    // Cast to access the data field
    struct AuthKeyTicket {
        unsigned int uiData;
    } *ticket = (struct AuthKeyTicket*)this_ptr;

    // Copy the entire data field directly
    ticket->uiData = uiSrc;
}
