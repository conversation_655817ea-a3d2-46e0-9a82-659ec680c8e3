/*
 * SetMiningTicketAuthKey.cpp
 * Original Function: MiningTicket::_AuthKeyTicket::Set
 * Original Address: 0x1400A6BA0
 * Original File: Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.cpp
 *
 * Sets the authentication key ticket data for mining operations.
 * Packs date/time information and usage count into a 32-bit data field using bit manipulation.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Set mining ticket authentication key data
 * @param this_ptr Pointer to MiningTicket::_AuthKeyTicket object
 * @param byYear Year value (14 bits, 0-16383)
 * @param byMonth Month value (4 bits, 0-15)
 * @param byDay Day value (5 bits, 0-31)
 * @param byHour Hour value (5 bits, 0-31)
 * @param byNumofTime Number of times used (4 bits, 0-15)
 */
void SetMiningTicketAuthKey(void* this_ptr, unsigned __int16 byYear, char by<PERSON><PERSON>h, char byDay, char byHour, char byNumofTime) 
{
    // Cast to access the data field
    struct AuthKeyTicket {
        unsigned int uiData;
    } *ticket = (struct AuthKeyTicket*)this_ptr;

    // Pack year (14 bits, bits 18-31) - supports years 0-16383
    ticket->uiData = ((byYear & 0x3FFF) << 18) | (ticket->uiData & 0x3FFFF);

    // Pack month (4 bits, bits 14-17) - supports months 0-15
    ticket->uiData = ((byMonth & 0xF) << 14) | (ticket->uiData & 0xFFFC3FFF);

    // Pack day (5 bits, bits 9-13) - supports days 0-31
    ticket->uiData = ((byDay & 0x1F) << 9) | (ticket->uiData & 0xFFFFC1FF);

    // Pack hour (5 bits, bits 4-8) - supports hours 0-31
    ticket->uiData = ((byHour & 0x1F) << 4) | (ticket->uiData & 0xFFFFFE0F);

    // Pack number of times (4 bits, bits 0-3) - supports 0-15 uses
    ticket->uiData = (byNumofTime & 0xF) | (ticket->uiData & 0xFFFFFFF0);
}
