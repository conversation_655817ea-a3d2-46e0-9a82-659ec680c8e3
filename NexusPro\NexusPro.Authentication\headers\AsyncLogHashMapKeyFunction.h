/*
 * AsyncLogHashMapKeyFunction.h
 * Original Function: stdext::_Hmap_traits::_Kfn (STL extension template specialization)
 * Original Address: 0x1403C35E0
 * Original File: _Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.h
 *
 * Header for STL extension hash map key function for async log containers.
 */

#pragma once
#ifndef ASYNCLOGHASHMAPKEYFUNCTION_H
#define ASYNCLOGHASHMAPKEYFUNCTION_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * STL extension hash map key function for async log containers
 * @param this_ptr Pointer to the hash map traits object
 * @param _Val Pointer to the key-value pair
 * @return Pointer to the key extracted from the pair
 */
void* AsyncLogHashMapKeyFunction(void* this_ptr, void* _Val);

#endif // ASYNCLOGHASHMAPKEYFUNCTION_H
