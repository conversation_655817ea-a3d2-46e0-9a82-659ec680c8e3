/*
 * AsyncLogListIteratorDestroy.cpp
 * Original Function: std::_Destroy<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>>
 * Address: 0x1403C8C60
 *
 * STL _Destroy function for async log list iterators.
 * Destroys a list iterator by calling its scalar deleting destructor.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * STL _Destroy function for async log list iterators
 * Destroys a list iterator by calling its scalar deleting destructor
 * @param _Ptr Pointer to the iterator to destroy
 */
void AsyncLogListIteratorDestroy(void* _Ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    void* v4; // [sp+30h] [bp+8h]@1

    v4 = _Ptr;
    v1 = &v3;
    
    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }
    
    // Call scalar deleting destructor for the iterator
    AsyncLogListIteratorScalarDeletingDestructor(v4, 0);
}
