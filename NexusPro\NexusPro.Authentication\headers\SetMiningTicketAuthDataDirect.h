/*
 * SetMiningTicketAuthDataDirect.h
 * Original Function: MiningTicket::_AuthKeyTicket::Set (overload)
 * Original Address: 0x140078ED0
 * Original File: Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.h
 *
 * Header for setting mining ticket authentication data directly.
 */

#pragma once
#ifndef SETMININGTICKETAUTHDATADIRECT_H
#define SETMININGTICKETAUTHDATADIRECT_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * Set mining ticket authentication key data directly
 * @param this_ptr Pointer to MiningTicket::_AuthKeyTicket object
 * @param uiSrc Source data value to copy
 */
void SetMiningTicketAuthDataDirect(void* this_ptr, unsigned int uiSrc);

#endif // SETMININGTICKETAUTHDATADIRECT_H
