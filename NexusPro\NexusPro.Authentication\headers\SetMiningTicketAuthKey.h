/*
 * SetMiningTicketAuth<PERSON>ey.h
 * Original Function: MiningTicket::_AuthKeyTicket::Set
 * Original Address: 0x1400A6BA0
 * Original File: Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.h
 *
 * Header for setting mining ticket authentication key data.
 */

#pragma once
#ifndef SETMININGTICKETAUTHKEY_H
#define SETMININGTICKETAUTHKEY_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * Set mining ticket authentication key data
 * @param this_ptr Pointer to MiningTicket::_AuthKeyTicket object
 * @param byYear Year value (14 bits, 0-16383)
 * @param byMonth Month value (4 bits, 0-15)
 * @param byDay Day value (5 bits, 0-31)
 * @param byHour Hour value (5 bits, 0-31)
 * @param byNumofTime Number of times used (4 bits, 0-15)
 */
void SetMiningTicketAuthKey(void* this_ptr, unsigned __int16 byYear, char by<PERSON><PERSON>h, char byDay, char by<PERSON><PERSON>, char byNumofTime);

#endif // SETMININGTICKETAUTHKEY_H
