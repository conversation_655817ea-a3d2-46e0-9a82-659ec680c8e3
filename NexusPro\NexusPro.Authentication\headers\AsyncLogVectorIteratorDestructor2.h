/*
 * AsyncLogVectorIteratorDestructor2.h
 * Original Function: std::_Vector_iterator destructor (STL template specialization)
 * Original Address: 0x1403C4460
 * Original File: 1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.h
 *
 * Header for STL vector iterator destructor for async log containers.
 */

#pragma once
#ifndef ASYNCLOGVECTORITERATORDESTRUCTOR2_H
#define ASYNCLOGVECTORITERATORDESTRUCTOR2_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

void AsyncLogVectorIteratorDestructor2(void* this_ptr);

#endif // ASYNCLOGVECTORITERATORDESTRUCTOR2_H
