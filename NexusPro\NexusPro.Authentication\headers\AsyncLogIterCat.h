/*
 * AsyncLogIterCat.h
 * Original Function: std::_Iter_cat (STL template specialization)
 * Original Address: 0x1403C8060
 * Original File: _Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.h
 *
 * Header for STL iterator category function for async log containers.
 */

#pragma once
#ifndef ASYNCLOGITERCAT_H
#define ASYNCLOGITERCAT_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

int AsyncLogIterCat(void* _Iter);

#endif // ASYNCLOGITERCAT_H
