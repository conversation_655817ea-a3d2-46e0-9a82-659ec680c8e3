/*
 * AsyncLogIterRandom.h
 * Original Function: std::_Iter_random (STL template specialization)
 * Original Address: 0x1403C8470
 * Original File: _Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.h
 *
 * Header for STL iterator random access function for async log containers.
 */

#pragma once
#ifndef ASYNCLOGITERRANDOM_H
#define ASYNCLOGITERRANDOM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

void AsyncLogIterRandom(void* _Iter);

#endif // ASYNCLOGITERRANDOM_H
