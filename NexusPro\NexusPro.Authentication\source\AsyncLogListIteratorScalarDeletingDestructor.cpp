/*
 * AsyncLogListIteratorScalarDeletingDestructor.cpp
 * Original Function: std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>::scalar deleting destructor
 * Address: 0x1403C8CB0
 *
 * <PERSON><PERSON><PERSON> deleting destructor for async log list iterator.
 * Calls destructor and optionally deletes the iterator memory.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * <PERSON><PERSON><PERSON> deleting destructor for async log list iterator
 * Calls destructor and optionally deletes the iterator memory
 * @param this Pointer to the iterator object
 * @param a2 Deletion flags (bit 0 = delete memory)
 * @return Pointer to the iterator (for chaining)
 */
void* AsyncLogListIteratorScalarDeletingDestructor(void* this_ptr, int a2)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    void* v6; // [sp+30h] [bp+8h]@1
    int v7; // [sp+38h] [bp+10h]@1

    v7 = a2;
    v6 = this_ptr;
    v2 = &v5;
    
    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Call the iterator destructor
    AsyncLogListIteratorDestructor(v6);
    
    // Delete memory if requested
    if (v7 & 1) {
        operator delete(v6);
    }
    
    return v6;
}
