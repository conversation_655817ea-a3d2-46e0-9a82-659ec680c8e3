/*
 * AsyncLogDestroyRange.cpp
 * Original Function: std::_Destroy_range<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>>
 * Address: 0x1403C7BD0
 *
 * STL _Destroy_range function for async log list iterators.
 * Destroys a range of list iterators using the appropriate iterator category.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * STL _Destroy_range function for async log list iterators
 * Destroys a range of list iterators using the appropriate iterator category
 * @param _First Iterator to beginning of range to destroy
 * @param _Last Iterator to end of range to destroy
 * @param _Al Allocator to use for destruction
 */
void AsyncLogDestroyRange(void* _First, void* _Last, void* _Al)
{
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-38h]@1
    int v6; // [sp+20h] [bp-18h]@4
    void* __formal; // [sp+40h] [bp+8h]@1
    void* _Lasta; // [sp+48h] [bp+10h]@1
    void* _Ala; // [sp+50h] [bp+18h]@1

    _Ala = _Al;
    _Lasta = _Last;
    __formal = _First;
    v3 = &v5;
    
    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    // Get pointer category for the iterator type
    v6 = AsyncLogPtrCat(&__formal, &_Lasta);
    
    // Call the appropriate destroy range function based on iterator category
    AsyncLogDestroyRangeWithCategory(__formal, _Lasta, _Ala, v6);
}
